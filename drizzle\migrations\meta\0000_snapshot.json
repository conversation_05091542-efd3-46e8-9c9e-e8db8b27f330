{"id": "c5e750ea-7004-4a98-bcbe-75d28b31578d", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.contact_messages": {"name": "contact_messages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "form_type": {"name": "form_type", "type": "text", "primaryKey": false, "notNull": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.guides": {"name": "guides", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "title_es": {"name": "title_es", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "description_es": {"name": "description_es", "type": "text", "primaryKey": false, "notNull": true}, "form_type": {"name": "form_type", "type": "text", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "skill_level": {"name": "skill_level", "type": "text", "primaryKey": false, "notNull": true}, "featured": {"name": "featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "online_filing": {"name": "online_filing", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.testimonials": {"name": "testimonials", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "case_type": {"name": "case_type", "type": "text", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "testimonial": {"name": "testimonial", "type": "text", "primaryKey": false, "notNull": true}, "timeline": {"name": "timeline", "type": "text", "primaryKey": false, "notNull": false}, "allow_contact": {"name": "allow_contact", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.translation_orders": {"name": "translation_orders", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order_number": {"name": "order_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "customer_email": {"name": "customer_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "customer_phone": {"name": "customer_phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "original_file_name": {"name": "original_file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "file_type": {"name": "file_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "page_count": {"name": "page_count", "type": "integer", "primaryKey": false, "notNull": true}, "delivery_type": {"name": "delivery_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "total_price": {"name": "total_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "original_file_path": {"name": "original_file_path", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "translated_file_path": {"name": "translated_file_path", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "admin_notes": {"name": "admin_notes", "type": "text", "primaryKey": false, "notNull": false}, "payment_intent_id": {"name": "payment_intent_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"translation_orders_order_number_unique": {"name": "translation_orders_order_number_unique", "nullsNotDistinct": false, "columns": ["order_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.uscis_data": {"name": "uscis_data", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "form_type": {"name": "form_type", "type": "text", "primaryKey": false, "notNull": true}, "fee": {"name": "fee", "type": "text", "primaryKey": false, "notNull": true}, "processing_time": {"name": "processing_time", "type": "text", "primaryKey": false, "notNull": true}, "last_updated": {"name": "last_updated", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"uscis_data_form_type_unique": {"name": "uscis_data_form_type_unique", "nullsNotDistinct": false, "columns": ["form_type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}