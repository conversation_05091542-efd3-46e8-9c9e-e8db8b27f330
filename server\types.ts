import { Request } from "express";

// Extend the Express Request interface to include session user
declare module "express-session" {
  interface SessionData {
    userId?: number;
    username?: string;
    isAuthenticated?: boolean;
  }
}

export interface AuthenticatedRequest extends Request {
  session: {
    userId?: number;
    username?: string;
    isAuthenticated?: boolean;
    save: (callback?: (err?: any) => void) => void;
    destroy: (callback?: (err?: any) => void) => void;
  };
}
