services:
  - type: web
    name: guia-immigration
    env: node
    plan: starter
    buildCommand: npm install && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: RENDER
        value: true
      - key: SESSION_SECRET
        generateValue: true
      - key: STRIPE_SECRET_KEY
        sync: false
      - key: PERPLEXITY_API_KEY
        sync: false
      - key: DATABASE_URL
        sync: false
    healthCheckPath: /api/health 